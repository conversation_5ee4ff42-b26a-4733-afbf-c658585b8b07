'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useApp } from '../contexts/AppContext';

const MoodSelector = ({ onMoodChange, className = '' }) => {
  const { state, setMood } = useApp();
  const [selectedMood, setSelectedMood] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  const moods = [
    {
      id: 'peaceful',
      name: 'Spokojn<PERSON>',
      emoji: '🧘‍♀️',
      description: 'Szukam wewnętrznego spokoju',
      colors: {
        primary: '#A8B5A0', // sage
        secondary: '#F8F6F3', // rice
        accent: '#E8D5C4' // lotus
      },
      energy: 'low'
    },
    {
      id: 'energetic',
      name: 'Energiczny',
      emoji: '⚡',
      description: 'Potrzebuję energii i motywacji',
      colors: {
        primary: '#D4A574', // golden
        secondary: '#FCF9F3', // first-ray
        accent: '#8B7355' // temple
      },
      energy: 'high'
    },
    {
      id: 'contemplative',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      emoji: '🌸',
      description: 'Chcę się zastanowić nad życiem',
      colors: {
        primary: '#9CAF88', // bamboo
        secondary: '#F0EDE8', // meditation-sky
        accent: '#6B5D4F' // palm-shadow
      },
      energy: 'medium'
    },
    {
      id: 'adventurous',
      name: 'Poszukujący',
      emoji: '🌟',
      description: 'Gotowy na nowe doświadczenia',
      colors: {
        primary: '#8B7355', // temple
        secondary: '#F9F7F5', // dawn-mist
        accent: '#D4A574' // golden
      },
      energy: 'high'
    },
    {
      id: 'healing',
      name: 'Uzdrawiający',
      emoji: '💚',
      description: 'Potrzebuję uzdrowienia i regeneracji',
      colors: {
        primary: '#A8B5A0', // monastery-moss
        secondary: '#F5F2ED', // shell
        accent: '#9CAF88' // bamboo
      },
      energy: 'low'
    }
  ];

  useEffect(() => {
    // Load saved mood from localStorage
    const savedMood = localStorage.getItem('bakasana-mood');
    if (savedMood) {
      const mood = moods.find(m => m.id === savedMood);
      if (mood) {
        setSelectedMood(mood);
        applyMoodColors(mood);
      }
    }
    
    // Show selector after a delay
    const timer = setTimeout(() => setIsVisible(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  const applyMoodColors = (mood) => {
    if (!mood) return;
    
    const root = document.documentElement;
    root.style.setProperty('--mood-primary', mood.colors.primary);
    root.style.setProperty('--mood-secondary', mood.colors.secondary);
    root.style.setProperty('--mood-accent', mood.colors.accent);
    
    // Add mood class to body for CSS targeting
    document.body.className = document.body.className.replace(/mood-\w+/g, '');
    document.body.classList.add(`mood-${mood.id}`);
  };

  const handleMoodSelect = (mood) => {
    setSelectedMood(mood);
    setMood(mood.id); // Update context
    localStorage.setItem('bakasana-mood', mood.id);
    applyMoodColors(mood);
    onMoodChange && onMoodChange(mood);
    
    // Hide selector after selection
    setTimeout(() => setIsVisible(false), 2000);
  };

  const resetMood = () => {
    setSelectedMood(null);
    setMood(null); // Update context
    localStorage.removeItem('bakasana-mood');
    document.body.className = document.body.className.replace(/mood-\w+/g, '');
    
    const root = document.documentElement;
    root.style.removeProperty('--mood-primary');
    root.style.removeProperty('--mood-secondary');
    root.style.removeProperty('--mood-accent');
    
    onMoodChange && onMoodChange(null);
  };

  if (!isVisible && !selectedMood) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className={`fixed bottom-20 left-4 right-4 md:left-auto md:right-8 md:w-80 z-40 ${className}`}
      >
        <div className="bg-white/90 backdrop-blur-md rounded-2xl border border-temple/20 p-6 shadow-xl">
          {!selectedMood ? (
            <>
              <div className="text-center mb-6">
                <h3 className="text-lg font-serif text-temple mb-2">
                  Jak się dziś czujesz?
                </h3>
                <p className="text-sm text-temple/70">
                  Dostosujemy doświadczenie do Twojego nastroju
                </p>
              </div>

              <div className="grid grid-cols-2 gap-3">
                {moods.map((mood) => (
                  <motion.button
                    key={mood.id}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleMoodSelect(mood)}
                    className="p-4 bg-white/50 hover:bg-white/80 rounded-xl border border-temple/10 hover:border-temple/30 transition-all duration-300 text-center group"
                  >
                    <div className="text-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
                      {mood.emoji}
                    </div>
                    <div className="text-sm font-medium text-temple mb-1">
                      {mood.name}
                    </div>
                    <div className="text-xs text-temple/60 leading-tight">
                      {mood.description}
                    </div>
                  </motion.button>
                ))}
              </div>

              <button
                onClick={() => setIsVisible(false)}
                className="w-full mt-4 text-xs text-temple/50 hover:text-temple/70 transition-colors duration-200"
              >
                Pomiń
              </button>
            </>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center"
            >
              <div className="text-3xl mb-3">{selectedMood.emoji}</div>
              <h3 className="text-lg font-serif text-temple mb-2">
                Nastrój: {selectedMood.name}
              </h3>
              <p className="text-sm text-temple/70 mb-4">
                Strona dostosowała się do Twojego nastroju
              </p>
              
              <div className="flex gap-2">
                <button
                  onClick={() => setIsVisible(true)}
                  className="flex-1 px-4 py-2 bg-temple/10 hover:bg-temple/20 text-temple text-sm rounded-lg transition-colors duration-200"
                >
                  Zmień
                </button>
                <button
                  onClick={resetMood}
                  className="flex-1 px-4 py-2 border border-temple/20 hover:border-temple/40 text-temple text-sm rounded-lg transition-colors duration-200"
                >
                  Reset
                </button>
              </div>
            </motion.div>
          )}
        </div>

        {/* Mood indicator */}
        {selectedMood && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 w-6 h-6 bg-temple/80 text-white rounded-full flex items-center justify-center text-xs"
          >
            {selectedMood.emoji}
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default MoodSelector;
