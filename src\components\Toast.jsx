'use client';

import { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast as useToastContext } from '../contexts/ToastContext';

const Toast = ({ 
  message, 
  type = 'success', 
  duration = 4000, 
  onClose,
  isVisible = false 
}) => {
  const [show, setShow] = useState(isVisible);
  const [progress, setProgress] = useState(100);

  const handleClose = useCallback(() => {
    setShow(false);
    setTimeout(() => onClose?.(), 300);
  }, [onClose]);

  useEffect(() => {
    setShow(isVisible);
    
    if (isVisible && duration > 0) {
      // Progress bar animation
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev - (100 / (duration / 50));
          return newProgress <= 0 ? 0 : newProgress;
        });
      }, 50);

      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => {
        clearTimeout(timer);
        clearInterval(progressInterval);
      };
    }
  }, [isVisible, duration, handleClose]);

  // Reset progress when showing
  useEffect(() => {
    if (isVisible) {
      setProgress(100);
    }
  }, [isVisible]);

  const getToastStyles = () => {
    const baseStyles = 'fixed top-4 right-4 z-[9999] min-w-80 max-w-md backdrop-blur-sm border-l-4 shadow-lg rounded-lg overflow-hidden';
    
    switch (type) {
      case 'success':
        return `${baseStyles} bg-white/95 border-l-emerald-500 text-gray-800`;
      case 'error':
        return `${baseStyles} bg-white/95 border-l-red-500 text-gray-800`;
      case 'warning':
        return `${baseStyles} bg-white/95 border-l-amber-500 text-gray-800`;
      case 'info':
        return `${baseStyles} bg-white/95 border-l-blue-500 text-gray-800`;
      default:
        return `${baseStyles} bg-white/95 border-l-gray-500 text-gray-800`;
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
        return 'ℹ';
      default:
        return '●';
    }
  };

  const getProgressColor = () => {
    switch (type) {
      case 'success':
        return 'bg-emerald-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-amber-500';
      case 'info':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, y: -50, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -50, scale: 0.95 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.3
          }}
          className={getToastStyles()}
        >
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0 mr-3">
                  <span className="text-lg font-medium">{getIcon()}</span>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium leading-relaxed">
                    {message}
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100/50"
                aria-label="Zamknij powiadomienie"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* Progress bar */}
          {duration > 0 && (
            <div className="h-1 bg-gray-200/50">
              <motion.div
                className={`h-full ${getProgressColor()}`}
                initial={{ width: '100%' }}
                animate={{ width: `${progress}%` }}
                transition={{ ease: "linear", duration: 0.05 }}
              />
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Hook do zarządzania toast-ami - deleguje do kontekstu
export const useToast = () => {
  return useToastContext();
};

export default Toast;
